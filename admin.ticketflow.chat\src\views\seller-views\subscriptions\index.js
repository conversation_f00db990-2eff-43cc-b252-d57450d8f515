import React, { useEffect, useState } from 'react';
import { Badge, <PERSON>ton, Card, Col, Row, Spin } from 'antd';
import subscriptionService from 'services/seller/subscriptions';
import { shallowEqual, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { disableRefetch } from 'redux/slices/menu';
import SellerSubscriptionModal from './subscriptionModal';

const features = [];

export default function SellerSubscriptions() {
  const { t } = useTranslation();
  const { defaultCurrency } = useSelector(
    (state) => state.currency,
    shallowEqual,
  );
  const { myShop } = useSelector((state) => state.myShop, shallowEqual);
  const [modal, setModal] = useState(null);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const colCount = data.length;
  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);
  const dispatch = useDispatch();

  const fetchSubscriptionList = () => {
    setLoading(true);
    subscriptionService
      .getAll()
      .then((res) => {
        setData(res.data);
      })
      .finally(() => {
        setLoading(false);
        dispatch(disableRefetch(activeMenu));
      });
  };

  useEffect(() => {
    if (activeMenu.refetch) {
      fetchSubscriptionList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeMenu.refetch]);

  return (
    <>
      <Card className='h-100'>
        {!loading ? (
          <div>
            <div className='text-center mb-4'>
              <h2 className='font-weight-semibold'>{t('subscription.choose.plan')}</h2>
              <Row type='flex' justify='center'>
                <Col sm={24} md={12} lg={8}>
                  <p>
                    {t('subscription.description')}
                  </p>
                </Col>
              </Row>
            </div>
            <Row>
              {data.map((elm, i) => (
                <Col
                  key={`price-column-${i}`}
                  span={6}
                  className={colCount === i + 1 ? '' : 'border-right'}
                >
                  <Badge.Ribbon
                    text={t('active')}
                    color='red'
                    className={
                      myShop?.subscription?.subscription?.id === elm.id
                        ? ''
                        : 'd-none'
                    }
                  >
                    <div className='p-3'>
                      <div className='text-center'>
                        <h1 className='display-4 mt-4'>
                          {defaultCurrency?.position === 'before' && (
                            <span
                              className='font-size-md d-inline-block mr-1'
                              style={{ transform: 'translate(0px, -17px)' }}
                            >
                              {defaultCurrency?.symbol}
                            </span>
                          )}
                          <span>{elm.price}</span>
                          {defaultCurrency?.position === 'after' && (
                            <span
                              className='font-size-md d-inline-block mr-1'
                              style={{ transform: 'translate(0px, -17px)' }}
                            >
                              {defaultCurrency?.symbol}
                            </span>
                          )}
                        </h1>
                        <p className='mb-0'>
                          <strong>{elm.month}</strong> {elm.month === 1 ? t('subscription.month.singular') : t('subscription.month.plural')}
                        </p>
                        <p className='mb-0'>
                          <strong>{elm?.order_limit?.toLocaleString('pt-BR')}</strong> {t('subscription.orders.per.month')}
                        </p>
                        <p className='mb-0'>
                          <strong>{elm?.product_limit?.toLocaleString('pt-BR')}</strong> {t('subscription.products.limit')}
                        </p>
                        <p className='mb-0'>
                          {Boolean(elm?.with_report)
                            ? t('subscription.with.reports')
                            : t('subscription.without.reports')}
                        </p>
                      </div>
                      <div className='mt-4'>
                        <h2 className='text-center font-weight-semibold'>
                          {elm.title || elm.type}
                        </h2>
                      </div>
                      <div className='d-flex justify-content-center mt-3'>
                        <div>
                          {features?.map((elm, i) => {
                            return (
                              <p key={`pricing-feature-${i}`}>
                                <Badge color={'blue'} />
                                <span>{elm}</span>
                              </p>
                            );
                          })}
                        </div>
                      </div>
                      <div className='mt-3 text-center'>
                        <Button
                          type='primary'
                          size='large'
                          onClick={() => setModal(elm)}
                          disabled={myShop?.subscription?.subscription?.id === elm.id}
                        >
                          {myShop?.subscription?.subscription?.id === elm.id
                            ? t('subscription.current.plan')
                            : t('subscription.choose.plan.button')
                          }
                        </Button>
                      </div>
                    </div>
                  </Badge.Ribbon>
                </Col>
              ))}
            </Row>
          </div>
        ) : (
          <Col className='ant-col-spin d-flex justify-content-center'>
            <Spin size='large' />
          </Col>
        )}
      </Card>
      {modal && (
        <SellerSubscriptionModal
          modal={modal}
          handleCancel={() => setModal(null)}
          refetch={fetchSubscriptionList}
        />
      )}
    </>
  );
}
