<?php

namespace App\Services\SubscriptionService;

use App\Helpers\ResponseError;
use App\Models\Shop;
use App\Models\ShopSubscription;
use App\Models\Subscription;
use App\Services\CoreService;
use DB;
use Illuminate\Database\Eloquent\Model;
use Throwable;

class SubscriptionService extends CoreService
{
    protected function getModelClass(): string
    {
        return Subscription::class;
    }

    /**
     * @param array $data
     * @return array
     */
    public function create(array $data): array
    {
        try {
            $subscription = $this->model()->create($data);

            return ['status' => true, 'code' => ResponseError::NO_ERROR, 'data' => $subscription];

        } catch (Throwable $e) {
            $this->error($e);
            return [
                'status'  => false,
                'code'    => ResponseError::ERROR_501,
                'message' => __('errors.' . ResponseError::ERROR_501, locale: $this->language)
            ];
        }
    }

    /**
     * @param Subscription $subscription
     * @param array $data
     * @return array
     */
    public function update(Subscription $subscription, array $data): array
    {
        try {
            $subscription->update($data);

            return ['status' => true, 'code' => ResponseError::NO_ERROR, 'data' => $subscription];

        } catch (Throwable $e) {
            $this->error($e);
            return [
                'status'  => false,
                'code'    => ResponseError::ERROR_502,
                'message' => __('errors.' . ResponseError::ERROR_502, locale: $this->language)
            ];
        }
    }

    /**
     * Attach a subscription to a shop
     *
     * @param int $subscriptionId
     * @param int $shopId
     * @return array
     */
    public function attach(int $subscriptionId, int $shopId): array
    {
        try {
            $subscription = Subscription::find($subscriptionId);

            if (!$subscription) {
                return [
                    'status'  => false,
                    'code'    => ResponseError::ERROR_404,
                    'message' => __('errors.' . ResponseError::ERROR_404, locale: $this->language)
                ];
            }

            if (!$subscription->active) {
                return [
                    'status'  => false,
                    'code'    => ResponseError::ERROR_400,
                    'message' => 'Subscription plan is not active'
                ];
            }

            // Check if shop already has an active subscription
            $existingSubscription = ShopSubscription::where('shop_id', $shopId)
                ->where('active', 1)
                ->where('expired_at', '>=', now())
                ->first();

            if ($existingSubscription) {
                return [
                    'status'  => false,
                    'code'    => ResponseError::ERROR_208,
                    'message' => __('errors.' . ResponseError::ERROR_208, locale: $this->language)
                ];
            }

            // Create shop subscription record
            $shopSubscription = ShopSubscription::create([
                'shop_id' => $shopId,
                'subscription_id' => $subscriptionId,
                'price' => $subscription->price,
                'type' => $subscription->type,
                'expired_at' => now()->addMonths($subscription->month),
                'active' => 0, // Will be activated after payment
            ]);

            return ['status' => true, 'code' => ResponseError::NO_ERROR, 'data' => $shopSubscription];

        } catch (Throwable $e) {
            $this->error($e);
            return [
                'status'  => false,
                'code'    => ResponseError::ERROR_501,
                'message' => __('errors.' . ResponseError::ERROR_501, locale: $this->language)
            ];
        }
    }


}
