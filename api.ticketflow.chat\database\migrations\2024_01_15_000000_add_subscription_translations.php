<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\Translation;

class AddSubscriptionTranslations extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $translations = [
            // English translations
            [
                'group' => 'web',
                'key' => 'subscription.choose.plan',
                'locale' => 'en',
                'value' => 'Choose Your Restaurant Plan',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.description',
                'locale' => 'en',
                'value' => 'Select the perfect plan to grow your restaurant business. Increase your orders, expand your menu, and access powerful analytics.',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.month.singular',
                'locale' => 'en',
                'value' => 'month',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.month.plural',
                'locale' => 'en',
                'value' => 'months',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.orders.per.month',
                'locale' => 'en',
                'value' => 'orders per month',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.products.limit',
                'locale' => 'en',
                'value' => 'products maximum',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.with.reports',
                'locale' => 'en',
                'value' => '✓ Advanced Analytics',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.without.reports',
                'locale' => 'en',
                'value' => '✗ Basic Analytics',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.current.plan',
                'locale' => 'en',
                'value' => 'Current Plan',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.choose.plan.button',
                'locale' => 'en',
                'value' => 'Choose This Plan',
                'status' => 1,
            ],

            // Portuguese translations
            [
                'group' => 'web',
                'key' => 'subscription.choose.plan',
                'locale' => 'pt-BR',
                'value' => 'Escolha Seu Plano de Restaurante',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.description',
                'locale' => 'pt-BR',
                'value' => 'Selecione o plano perfeito para fazer seu restaurante crescer. Aumente seus pedidos, expanda seu cardápio e tenha acesso a relatórios avançados.',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.month.singular',
                'locale' => 'pt-BR',
                'value' => 'mês',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.month.plural',
                'locale' => 'pt-BR',
                'value' => 'meses',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.orders.per.month',
                'locale' => 'pt-BR',
                'value' => 'pedidos por mês',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.products.limit',
                'locale' => 'pt-BR',
                'value' => 'produtos no máximo',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.with.reports',
                'locale' => 'pt-BR',
                'value' => '✓ Relatórios Avançados',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.without.reports',
                'locale' => 'pt-BR',
                'value' => '✗ Relatórios Básicos',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.current.plan',
                'locale' => 'pt-BR',
                'value' => 'Plano Atual',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'subscription.choose.plan.button',
                'locale' => 'pt-BR',
                'value' => 'Escolher Este Plano',
                'status' => 1,
            ],
            // Additional common translations
            [
                'group' => 'web',
                'key' => 'active',
                'locale' => 'pt-BR',
                'value' => 'Ativo',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'purchase',
                'locale' => 'pt-BR',
                'value' => 'Adquirir',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'month',
                'locale' => 'pt-BR',
                'value' => 'mês',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'active',
                'locale' => 'en',
                'value' => 'Active',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'purchase',
                'locale' => 'en',
                'value' => 'Purchase',
                'status' => 1,
            ],
            [
                'group' => 'web',
                'key' => 'month',
                'locale' => 'en',
                'value' => 'month',
                'status' => 1,
            ],
        ];

        foreach ($translations as $translation) {
            Translation::updateOrCreate(
                [
                    'group' => $translation['group'],
                    'key' => $translation['key'],
                    'locale' => $translation['locale'],
                ],
                $translation
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $keys = [
            'subscription.choose.plan',
            'subscription.description',
            'subscription.month.singular',
            'subscription.month.plural',
            'subscription.orders.per.month',
            'subscription.products.limit',
            'subscription.with.reports',
            'subscription.without.reports',
            'subscription.current.plan',
            'subscription.choose.plan.button',
            'active',
            'purchase',
            'month',
        ];

        Translation::whereIn('key', $keys)->delete();
    }
}
